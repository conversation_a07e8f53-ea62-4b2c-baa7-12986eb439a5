---
tags:
  - anki
  - AI
  - Artifical_Intelligence
  - AI_ANKI
---
### 通过正向卷积操作，输入和输出建立了怎样的关系？
```ad-question
title: 
```
?
```ad-success
collapse: close
![[卷积和转置卷积#^1ef9f2]]
```
<!--SR:!2025-08-01,12,270-->


## 评价指标

### 混淆矩阵是如何绘制的，其中的 TP, TN, FP, FN 分别表示什么呢？
?
```ad-success
title: 答案
collapse: close
![[评价指标#^5uu670]]
![[评价指标#二分类混淆矩阵]]
```
<!--SR:!2025-08-04,15,290-->


### 如何通过混淆矩阵计算 Accuracy, Precision, Recall?
?
```ad-success
collapse: close
![[评价指标#推导公式]]
```
<!--SR:!2025-08-04,15,290-->


## 激活函数

### 激活函数的作用是什么？
?
```ad-success
title: 答案
collapse: close
![[激活函数#激活函数的作用]] 
```
<!--SR:!2025-08-05,16,290-->


### ReLU 激活函数是怎样的，其作用是什么，主要作用于什么地方？
?
```ad-success
title: 答案
collapse: close
 ![[激活函数#ReLU（Rectified Linear Unit，线性整流函数）]]
```
<!--SR:!2025-07-31,11,270-->


### Sigmoid 激活函数的图像是怎样的呢，其作用是什么呢？
?
```ad-success
title: 答案
collapse: close
![[激活函数#Sigmoid]] 
```
<!--SR:!2025-07-30,10,270-->


### Softmax 函数的图像是怎样的呢，其作用是什么呢？
?
```ad-success
title: 答案
collapse: close
![[激活函数#softmax]] 
```
<!--SR:!2025-08-03,14,290-->

## 思考下什么是均方误差呢
?
```ad-success
title: 答案
collapse: close
 均方误差是损失函数的一种，用于评估所有真实值 $y$ 与预测值 $\hat{y}$ 之间差值的平方和
 ![[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function]]
```
end


