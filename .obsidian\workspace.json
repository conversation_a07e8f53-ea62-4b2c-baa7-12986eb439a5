{"main": {"id": "9a92b85be99e6512", "type": "split", "children": [{"id": "54be41f278c69a1b", "type": "tabs", "children": [{"id": "6c894bef37873549", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Artificial Intelligence/mcp.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "mcp"}}, {"id": "70efa6c269d0a5ed", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/An<PERSON>/Artificial Intelligence/未命名.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "未命名"}}, {"id": "d963dd5afa4616d5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "5. 逻辑回归（Logisitic Regression)"}}, {"id": "9c93fa971ed8f0fd", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "1. 线性模型（Linear Model）"}}], "currentTab": 3}], "direction": "vertical"}, "left": {"id": "ccafe5c1edb39392", "type": "split", "children": [{"id": "ff985ebb9e64a67a", "type": "tabs", "children": [{"id": "0471e214e11a0e3e", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "df897e713d50ad26", "type": "leaf", "state": {"type": "search", "state": {"query": "mse", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "byModifiedTime"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "a54f5d1b66aa9af0", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 280.5}, "right": {"id": "589685af648bfa09", "type": "split", "children": [{"id": "ebf068d98b8f5d7e", "type": "tabs", "dimension": 67.8921568627451, "children": [{"id": "503da8d536964a67", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "反向链接"}}, {"id": "4eb73336137b77fc", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "出链"}}, {"id": "dd44a90d63472d6d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "3c9fff9b1aac3ed1", "type": "leaf", "state": {"type": "copilot-chat-view", "state": {}, "icon": "message-square", "title": "Copilot"}}, {"id": "01747ffb46c2e2db", "type": "leaf", "state": {"type": "comment-view", "state": {}, "icon": "highlighter", "title": "<PERSON><PERSON><PERSON>"}}, {"id": "30bb109630e92a1d", "type": "leaf", "state": {"type": "chinese-calendar-view", "state": {}, "icon": "calendar-with-checkmark", "title": "日历"}}, {"id": "d6fd2d8d71bdc2af", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "SpacedRepIcon", "title": "笔记复习序列"}}], "currentTab": 6}, {"id": "362ffd7addd9ee9d", "type": "tabs", "dimension": 32.1078431372549, "children": [{"id": "4734f418b64acbcd", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}, "icon": "spreadsheet", "title": "Advanced Tables"}}, {"id": "6b8468d83e031abe", "type": "leaf", "state": {"type": "outline", "state": {"file": "文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "算法卡 -CIOU（完全交并比，complete intersection over union）- 的大纲"}}]}], "direction": "horizontal", "width": 200, "collapsed": true}, "left-ribbon": {"hiddenItems": {"hi-note:HiNote": false, "pdf-plus:PDF++: Toggle auto-copy": false, "pdf-plus:PDF++: Toggle auto-focus": false, "pdf-plus:PDF++: Toggle auto-paste": false, "copilot:Open Copilot Chat": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian-excalidraw-plugin:新建绘图文件": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "obsidian-spaced-repetition:复习卡片": false, "obsidian-kanban:创建新看板": false, "omnisearch:Omnisearch": false, "templater-obsidian:Templater": false, "homepage:Open homepage": false}}, "active": "9c93fa971ed8f0fd", "lastOpenFiles": ["学习库/An<PERSON>/Artificial Intelligence/未命名.md", "学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md", "学习库/Artificial Intelligence/mcp.md", "学习库/An<PERSON>/Artificial Intelligence/未命名 1.md", "学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md", "学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md", "Home/Home.md", "工作库/项目/舌诊/人脸识别.md", "components/logs/2025-07-31.components.log", "学习库/linux/wsl2.md", "学习库/c/2 核心语法.md", "学习库/c/1 从这开始吧.md", "学习库/c/0 一些操作.md", "学习库/c/4 流程控制语句.md", "折腾库/未命名.md", "学习库/Artificial Intelligence/Prompt, Agent, MCP.md", "学习库/Docker/未命名.md", "学习库/Artificial Intelligence", "学习库/心得/文献检索指南.md", "学习库/心得/缝合笔记.md", "学习库/心得/大队长手把手带你发论文_V2_工房版.pdf", "components/logs/2025-07-29.components.log", "components/logs/2025-07-28.components.log", "学习库/Latex/Latex 从入门到如土.md", "components/logs/2025-07-26.components.log", "学习库/软件使用/vscode/未命名.md", "学习库/软件使用/Total commander/使用手册.md", "学习库/软件使用/vscode", "components/logs/2025-07-24.components.log", "学习库/<PERSON><PERSON>/机器人学/未命名.md", "学习库/stm32/1 启动.md", "学习库/An<PERSON>/单词/python.md", "工作库/项目/舌诊/tensorRT.md", "学习库/Deep learning/概念库/卷积和转置卷积.md", "components/logs/2025-07-23.components.log", "components/logs/2025-07-22.components.log", "学习库/stm32/attachments/2 GPIO-2025-07-13-08-54-29.png", "学习库/stm32/attachments/2 GPIO-2025-07-13-08-53-44.png", "学习库/Deep learning/概念库/attachments/评价指标-2025-07-12-11-16-24.png", "学习库/Deep learning/概念库/attachments/评价指标-2025-07-12-11-13-53.png", "学习库/Deep learning/概念库/attachments/评价指标-2025-07-12-11-00-15.png", "学习库/Deep learning/概念库/attachments/激活函数-2025-07-12-10-08-54.png", "学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-21-37-16.png", "学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-25-06.png", "学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-01-26.png", "学习库/Deep learning/概念库/attachments/语义分割/Attachments/语义分割-2025-03-16-08-42-15.png"]}