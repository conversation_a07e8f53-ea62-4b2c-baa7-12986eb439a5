/* ===== 代码块光标颜色强化版 - Blue Topaz 兼容 ===== */

/* 只在代码块内覆盖光标颜色变量 */
.theme-dark .HyperMD-codeblock {
  --cursor-color: #00ff00 !important;
  --cursor-color-temp: #00ff00 !important;
  --vim-cursor: #00ff00 !important;
  --cursor: #00ff00 !important;
}

/* 代码块专用光标设置 */
.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock {
  --cursor-color: #00ff00 !important;
  --cursor-color-temp: #00ff00 !important;
  caret-color: #00ff00 !important;
}

/* Blue Topaz 光标样式覆盖 - 只在代码块内 */
.theme-dark .cm-s-obsidian .HyperMD-codeblock .cm-cursor,
.theme-dark .HyperMD-codeblock .CodeMirror-cursor,
.theme-dark .HyperMD-codeblock .cm-s-obsidian .cm-cursor,
.theme-dark .HyperMD-codeblock .cm-s-obsidian .cm-dropCursor {
  border-left-color: #00ff00 !important;
  border-left-width: 3px !important;
  border-left-style: solid !important;
  background: rgba(0, 255, 0, 0.2) !important;
  box-shadow: 0 0 8px rgba(0, 255, 0, 0.6) !important;
}

/* 代码块内容区域光标 */
.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock .cm-content {
  caret-color: #00ff00 !important;
}

/* 所有可能的光标选择器 - 最高优先级 */
.theme-dark .HyperMD-codeblock .cm-cursor,
.theme-dark .HyperMD-codeblock .cm-focused .cm-cursor,
.theme-dark .HyperMD-codeblock.cm-focused .cm-cursor,
.theme-dark .HyperMD-codeblock .cm-editor.cm-focused .cm-cursor,
.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock .cm-cursor {
  border-left: 3px solid #00ff00 !important;
  background: rgba(0, 255, 0, 0.2) !important;
  box-shadow: 0 0 8px rgba(0, 255, 0, 0.6) !important;
  animation: cursor-blink 1s infinite !important;
}

/* 光标闪烁动画 */
@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.4; }
}

.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock::selection,
.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock *::selection {
  background-color: rgba(255, 255, 255, 0.3) !important;
}

/* Vim模式光标颜色 - 更明显 */
.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock .cm-fat-cursor,
.theme-dark .markdown-source-view.mod-cm6 .HyperMD-codeblock .cm-animate-fat-cursor,
.theme-dark .HyperMD-codeblock .cm-fat-cursor,
.theme-dark .HyperMD-codeblock .cm-animate-fat-cursor {
  width: 0.6em;
  background: #00ff00 !important; /* 亮绿色 */
  opacity: 0.9 !important;
  box-shadow: 0 0 8px rgba(0, 255, 0, 0.6) !important; /* 强发光效果 */
}

/* 额外的光标样式覆盖 */
.theme-dark .cm-editor .HyperMD-codeblock .cm-cursor,
.theme-dark .cm-content .HyperMD-codeblock .cm-cursor,
.theme-dark .HyperMD-codeblock .cm-line .cm-cursor {
  border-left-color: #00ff00 !important;
  border-left-width: 3px !important;
  background: rgba(0, 255, 0, 0.2) !important;
  box-shadow: 0 0 5px rgba(0, 255, 0, 0.5) !important;
}

/* 通用光标颜色覆盖 - 最高优先级 */
.theme-dark .HyperMD-codeblock {
  caret-color: #00ff00 !important;
}

/* 强制覆盖 - 如果上面的都不生效，这个应该会生效 */
.theme-dark .HyperMD-codeblock *,
.theme-dark .HyperMD-codeblock,
.theme-dark .cm-line.HyperMD-codeblock,
.theme-dark .cm-line.HyperMD-codeblock * {
  caret-color: #00ff00 !important;
}

/* 最后的备用方案 - 全局代码块光标 */
body.theme-dark .HyperMD-codeblock .cm-cursor::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #00ff00 !important;
  box-shadow: 0 0 8px rgba(0, 255, 0, 0.8) !important;
  z-index: 999;
}

/* 只在代码块内设置光标颜色 - 限制作用域 */
.HyperMD-codeblock,
.HyperMD-codeblock .cm-line,
.markdown-source-view.mod-cm6 .HyperMD-codeblock {
  caret-color: #00ff00 !important;
}

/* 只在代码块内设置绿色光标 */
.HyperMD-codeblock .cm-editor.cm-focused .cm-cursor,
.HyperMD-codeblock .cm-editor .cm-cursor,
.HyperMD-codeblock .cm-content .cm-cursor,
.HyperMD-codeblock .cm-cursor {
  border-left: 2px solid #00ff00 !important;
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
  background: transparent !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 移除全局光标颜色设置，只在代码块内生效 */

/* 最终解决方案 - 使用 CSS 动画创建可见光标 */
.HyperMD-codeblock {
  position: relative;
}

.HyperMD-codeblock:focus-within::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 1.2em;
  background: #00ff00;
  animation: cursor-blink 1s infinite;
  pointer-events: none;
  z-index: 1000;
}

@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 代码块缩进线颜色 */
.markdown-source-view.mod-cm6 .HyperMD-codeblock .cm-indent::before {
  border-right: 1px solid rgba(255, 255, 255, 0.25) !important;
}

/* 代码块活动行缩进线颜色 */
.markdown-source-view.mod-cm6 .HyperMD-codeblock .cm-active .cm-indent::before {
  border-right: 1px solid rgba(255, 255, 255, 0.4) !important;
}

/* ===== 代码高亮颜色修复 ===== */

/* Python关键字颜色 (如 input, print, def, class 等) */
.theme-dark .HyperMD-codeblock .cm-keyword {
  color: #ff79c6 !important; /* 粉红色，更醒目 */
}

/* 函数名颜色 (如 input, print 等内置函数) */
.theme-dark .HyperMD-codeblock .cm-builtin {
  color: #50fa7b !important; /* 绿色 */
  font-weight: bold !important; /* 加粗 */
  text-shadow: 0 0 2px rgba(80, 250, 123, 0.5) !important; /* 轻微发光效果 */
}

/* 变量名颜色 */
.theme-dark .HyperMD-codeblock .cm-variable {
  color: #f8f8f2 !important; /* 白色 */
}

/* 字符串颜色 */
.theme-dark .HyperMD-codeblock .cm-string {
  color: #f1fa8c !important; /* 黄色 */
}

/* 数字颜色 */
.theme-dark .HyperMD-codeblock .cm-number {
  color: #bd93f9 !important; /* 紫色 */
}

/* 注释颜色 */
.theme-dark .HyperMD-codeblock .cm-comment {
  color: #6272a4 !important; /* 灰蓝色 */
}

/* 操作符颜色 (=, +, -, 等) */
.theme-dark .HyperMD-codeblock .cm-operator {
  color: #ff79c6 !important; /* 粉红色 */
}

/* 特殊的Python内置函数强化 */
.theme-dark .HyperMD-codeblock .cm-variable:is([title="input"], [title="print"], [title="len"], [title="range"], [title="str"], [title="int"], [title="float"]) {
  color: #50fa7b !important; /* 绿色，更突出 */
  font-weight: bold !important;
}

/* 通用的代码文本对比度增强 */
.theme-dark .HyperMD-codeblock {
  color: #f8f8f2 !important; /* 确保基础文本是白色 */
}

/* 针对可能的语法高亮类 */
.theme-dark .HyperMD-codeblock .token.function,
.theme-dark .HyperMD-codeblock .token.builtin {
  color: #50fa7b !important; /* 绿色 */
  font-weight: 500 !important;
}

/* ===== Blue Topaz 主题专用修复 ===== */

/* 只在代码块内覆盖 Blue Topaz 的代码颜色变量 */
.theme-dark .HyperMD-codeblock {
  --code-builtin: #50fa7b !important; /* 内置函数 - 绿色 */
  --code-function: #82aaff !important; /* 函数名 - 蓝色 */
  --code-keyword: #ff79c6 !important; /* 关键字 - 粉红色 */
  --code-string: #c3e88d !important; /* 字符串 - 浅绿色 */
  --code-variable: #f8f8f2 !important; /* 变量 - 白色 */
  --code-comment: #676e95 !important; /* 注释 - 灰蓝色 */
  --code-operator: #89ddff !important; /* 操作符 - 青色 */
  --code-number: #f78c6c !important; /* 数字 - 橙色 */
}

/* 确保内置函数在代码块中显示正确 */
.theme-dark .HyperMD-codeblock .cm-builtin,
.theme-dark .markdown-preview-view pre.cm-s-obsidian .cm-builtin,
.theme-dark .markdown-preview-view pre code span.builtin {
  color: var(--code-builtin) !important;
  font-weight: bold !important;
  text-shadow: 0 0 2px rgba(80, 250, 123, 0.3) !important;
}

/* ===== LaTeX 数学公式颜色修复 ===== */

/* 修复行内LaTeX公式中非数字字符的颜色 - 在编辑模式下 */
.theme-dark .cm-s-obsidian .cm-math.cm-atom {
  color: #82aaff !important; /* 蓝色，更好的可读性 */
}

/* LaTeX公式中的变量和字母 */
.theme-dark .cm-s-obsidian .cm-math.cm-variable {
  color: #f8f8f2 !important; /* 白色 */
}

/* LaTeX公式中的操作符 */
.theme-dark .cm-s-obsidian .cm-math.cm-operator {
  color: #ff79c6 !important; /* 粉红色 */
}

/* LaTeX公式中的关键字 */
.theme-dark .cm-s-obsidian .cm-math.cm-keyword {
  color: #50fa7b !important; /* 绿色 */
}

/* LaTeX公式中的函数名 */
.theme-dark .cm-s-obsidian .cm-math.cm-builtin {
  color: #50fa7b !important; /* 绿色 */
  font-weight: bold !important;
}

/* LaTeX公式中的数字保持原有颜色 */
.theme-dark .cm-s-obsidian .cm-math.cm-number {
  color: var(--code-value) !important; /* 使用主题的数字颜色 */
}

/* LaTeX公式中的字符串 */
.theme-dark .cm-s-obsidian .cm-math.cm-string {
  color: #f1fa8c !important; /* 黄色 */
}

/* 通用的LaTeX公式文本颜色增强 */
.theme-dark .cm-s-obsidian .cm-math {
  color: #f8f8f2 !important; /* 确保基础文本是白色 */
}

/* ===== 亮色模式下的 LaTeX 数学公式颜色修复 ===== */

/* 修复行内LaTeX公式中非数字字符的颜色 - 在亮色模式编辑时 */
.theme-light .cm-s-obsidian .cm-math.cm-atom {
  color: #0066cc !important; /* 深蓝色，在亮色背景下更易读 */
}

/* 亮色模式下LaTeX公式中的变量和字母 */
.theme-light .cm-s-obsidian .cm-math.cm-variable {
  color: #333333 !important; /* 深灰色 */
}

/* 亮色模式下LaTeX公式中的操作符 */
.theme-light .cm-s-obsidian .cm-math.cm-operator {
  color: #cc0066 !important; /* 深粉红色 */
}

/* 亮色模式下LaTeX公式中的关键字 */
.theme-light .cm-s-obsidian .cm-math.cm-keyword {
  color: #006600 !important; /* 深绿色 */
}

/* 亮色模式下LaTeX公式中的函数名 */
.theme-light .cm-s-obsidian .cm-math.cm-builtin {
  color: #006600 !important; /* 深绿色 */
  font-weight: bold !important;
}

/* 亮色模式下LaTeX公式中的数字保持原有颜色 */
.theme-light .cm-s-obsidian .cm-math.cm-number {
  color: var(--code-value) !important; /* 使用主题的数字颜色 */
}

/* 亮色模式下LaTeX公式中的字符串 */
.theme-light .cm-s-obsidian .cm-math.cm-string {
  color: #cc6600 !important; /* 橙色 */
}

/* 亮色模式下通用的LaTeX公式文本颜色增强 */
.theme-light .cm-s-obsidian .cm-math {
  color: #333333 !important; /* 确保基础文本是深灰色 */
}