---
tags:
  - 学习
  - Artificial_Intelligence
  - MCP
---

# MCP (Model Context Protocol) 学习笔记

> [!abstract] 核心概念
> **Model Context Protocol (MCP)** 是 Anthropic 开发的开放协议，让 AI 模型能够安全、标准化地调用外部工具，实现与真实世界的交互。

## 🎯 什么是 MCP

MCP 解决了 AI 模型的核心限制：**如何安全地与外部世界交互**

- **标准化接口** - 统一的工具调用规范
- **安全机制** - 内置权限控制和沙箱隔离
- **模块化设计** - 易于扩展和集成

## 🏗️ 核心架构

```mermaid
graph LR
    A[AI Application] --> B[MCP Client]
    B --> C[MCP Server]
    C --> D[External Tools]

    D --> E[Files]
    D --> F[APIs]
    D --> G[Databases]
```

**三个关键组件：**
- **MCP Client** - 与 AI 应用集成，发送请求
- **MCP Server** - 暴露工具，处理调用
- **External Tools** - 实际执行任务的工具

## 🛠️ 工具系统

### 工具定义示例
```json
{
  "name": "read_file",
  "description": "读取文件内容",
  "inputSchema": {
    "type": "object",
    "properties": {
      "path": {"type": "string"}
    }
  }
}
```

### 常见工具类型
- **文件操作** - 读写、搜索、管理
- **数据库** - 查询、更新、事务
- **网络请求** - API 调用、数据获取
- **系统命令** - 执行脚本、进程管理

## 🔐 安全特性

> [!warning] 安全机制
> - **权限控制** - 基于角色的访问管理
> - **沙箱隔离** - 限制工具执行环境
> - **审计日志** - 记录所有操作
> - **超时保护** - 防止长时间阻塞





---

> [!quote] 核心价值
> MCP 让 AI 从"聊天机器人"进化为"行动助手"，是构建实用 AI 应用的关键基础设施。