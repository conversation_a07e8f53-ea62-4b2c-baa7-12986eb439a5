---
tags:
  - 项目
  - 舌诊
  - 毕设
---
# 关键点检测
可以通过人脸的关键点，来确定人脸的朝向 [[学习库/ROS/机器人学/机器人运动学/未命名#3 1 示例一：ZYX动态欧拉角（Roll-Pitch-Yaw）的分步解析|欧拉角]]

## top-down

是一种 two-stage 的检测方法，首先进行人体检测，然后对对每一个检测到的人进行关键点检测
- **缺点**：复杂度随着人物数量的增加而增加
- **缺点**：先检测图像中人体的边界框，再在 bounding box 中检测关键点，如果出现遮挡或者小目标表现比较差
- **优点**：准确率高

## bottom-up

是一种基于热图的关键点检测方法，一次性检测所有的关键点，采用 NMS 在热图中寻找局部最大值
- **缺点**：无法进行 End-to-End 的训练，因为后处理是在卷积之外进行的 
- **缺点**：两个个体如果很接近，通过热图的关键点检测就很难区分关键点 （准确率低）
- **优点**：复杂度低，推理快

