2025-07-31 09:32:53.166 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:32:53.175 [info] indexing created file components/logs/2025-07-31.components.log  [object Object] 
2025-07-31 09:32:53.177 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:32:53.179 [info] refresh page data from created listeners 0 1017   
2025-07-31 09:32:53.180 [info] index finished after resolve  [object Object] 
2025-07-31 09:32:53.181 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:32:56.306 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:32:56.338 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:32:56.340 [info] index finished after resolve  [object Object] 
2025-07-31 09:32:56.341 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:32:59.015 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:32:59.031 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:32:59.033 [info] index finished after resolve  [object Object] 
2025-07-31 09:32:59.034 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:33:01.509 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:33:01.526 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:33:01.527 [info] index finished after resolve  [object Object] 
2025-07-31 09:33:01.528 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:16.641 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:16.658 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:16.659 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:16.660 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:18.792 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:18.820 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:18.821 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:18.821 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:20.826 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:20.846 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:20.850 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:20.850 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:23.282 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:23.289 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:23.289 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:23.290 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:34.288 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:34.292 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:34.293 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:34.293 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:37.214 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:37.218 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:37.219 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:37.220 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:39.211 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:39.214 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:39.215 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:39.216 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:42.634 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:42.638 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:42.638 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:42.639 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:46.830 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:46.835 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:46.837 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:46.837 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:51.990 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:51.995 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:51.996 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:51.997 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:55.143 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:55.147 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:55.148 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:55.148 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:57.228 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:57.235 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:57.235 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:57.236 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:59.291 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:59.295 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:59.295 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:59.296 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:01.391 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:01.395 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:01.396 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:01.396 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:06.868 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:06.872 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:06.872 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:06.873 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:08.904 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:08.908 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:08.910 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:08.910 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:11.584 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:11.588 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:11.589 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:11.589 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:13.851 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:13.856 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:13.857 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:13.857 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:16.283 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:16.286 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:16.286 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:16.287 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:19.562 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:19.568 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:19.569 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:19.569 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:23.357 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:23.375 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:23.375 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:23.376 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:26.174 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:26.179 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:26.180 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:26.181 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:31.725 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:31.729 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:31.730 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:31.731 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:33.787 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:33.790 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:33.791 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:33.792 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:36.165 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:36.169 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:36.170 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:36.170 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:38.416 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:38.419 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:38.421 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:38.421 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:41.361 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:41.365 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:41.366 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:41.367 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:43.536 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:43.540 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:43.540 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:43.541 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:59.291 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:59.309 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:59.312 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:59.312 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:02.703 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:02.708 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:02.709 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:02.709 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:05.356 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:05.360 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:05.361 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:05.362 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:07.509 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:07.513 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:07.514 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:07.514 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:09.583 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:09.587 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:09.588 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:09.588 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:11.661 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:11.665 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:11.666 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:11.666 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:13.799 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:13.803 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:13.805 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:13.806 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:17.667 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:17.670 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:17.671 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:17.672 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:30.174 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:30.178 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:30.179 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:30.180 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:33.124 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:33.128 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:33.129 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:33.130 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:35.345 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:35.349 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:35.350 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:35.350 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:37.486 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:37.490 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:37.491 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:37.492 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:59.627 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:59.631 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:59.632 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:59.633 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:01.921 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:01.925 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:01.926 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:01.927 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:04.543 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:04.549 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:04.550 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:04.550 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:06.640 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:06.644 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:06.645 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:06.645 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:09.067 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:09.070 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:09.071 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:09.071 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:11.635 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:11.639 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:11.640 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:11.640 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:13.925 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:13.929 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:13.930 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:13.931 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:16.350 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:16.353 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:16.354 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:16.354 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:20.482 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:20.486 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:20.486 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:20.487 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:26.938 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:26.941 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:26.943 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:26.944 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:30.497 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:30.501 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:30.502 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:30.502 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:32.917 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:32.921 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:32.922 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:32.922 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:35.587 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:35.591 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:35.592 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:35.592 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:38.031 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:38.034 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:38.035 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:38.035 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:42.667 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:42.672 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:42.673 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:42.673 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:44.768 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:44.773 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:44.773 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:44.774 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:46.826 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:46.830 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:46.831 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:46.831 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:49.009 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:49.013 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:49.014 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:49.015 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:51.096 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:51.100 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:51.101 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:51.101 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:53.965 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:53.969 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:53.970 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:53.970 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:52.586 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:52.590 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:52.591 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:52.591 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:54.909 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:54.913 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:54.916 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:54.921 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:57.086 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:57.090 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:57.091 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:57.091 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:59.577 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:59.581 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:59.583 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:59.583 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:01.786 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:01.790 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:01.791 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:01.792 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:08.315 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:08.319 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:08.319 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:08.320 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:10.472 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:10.476 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:10.477 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:10.478 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:12.608 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:12.612 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:12.613 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:12.613 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:14.681 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:14.685 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:14.686 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:14.687 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:16.726 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:16.729 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:16.730 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:16.730 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:18.789 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:18.793 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:18.793 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:18.794 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:20.862 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:20.866 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:20.867 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:20.867 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:22.885 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:22.889 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:22.890 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:22.890 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:09.279 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:09.283 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:09.284 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:09.284 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:31.061 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:31.065 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:31.066 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:31.067 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:34.512 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:34.516 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:34.517 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:34.517 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:37.499 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:37.501 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:37.502 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:37.502 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:40.543 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:40.547 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:40.548 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:40.548 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:43.286 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:43.290 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:43.291 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:43.291 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:47.100 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:47.104 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:47.105 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:47.105 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:49.297 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:49.301 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:49.302 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:49.302 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:51.682 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:51.686 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:51.686 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:51.687 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:53.972 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:53.976 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:53.976 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:53.977 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:56.057 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:56.061 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:56.062 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:56.062 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:00.188 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:00.192 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:00.193 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:00.194 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:08.125 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:08.128 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:08.129 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:08.129 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:10.528 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:10.530 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:10.531 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:10.531 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:12.836 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:12.841 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:12.842 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:12.842 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:17.276 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:17.279 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:17.280 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:17.281 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:20.833 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:20.837 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:20.838 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:20.839 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:22.875 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:22.880 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:22.881 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:22.881 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:27.754 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:27.757 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:27.758 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:27.759 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:29.956 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:29.961 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:29.961 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:29.962 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:32.002 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:32.006 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:32.008 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:32.009 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:35.046 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:35.049 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:35.050 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:35.051 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:37.323 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:37.328 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:37.330 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:37.330 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:39.599 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:39.602 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:39.603 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:39.603 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:43.541 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:43.545 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:43.546 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:43.546 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:45.697 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:45.700 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:45.702 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:45.702 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:50.209 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:50.213 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:50.214 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:50.214 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:52.321 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:52.325 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:52.325 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:52.326 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:54.353 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:54.357 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:54.358 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:54.358 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:50:15.744 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:50:15.748 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:50:15.749 [info] index finished after resolve  [object Object] 
2025-07-31 09:50:15.749 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:50:17.861 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:50:17.866 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:50:17.867 [info] index finished after resolve  [object Object] 
2025-07-31 09:50:17.867 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:24.723 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:24.751 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:24.752 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:24.753 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:27.326 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:27.329 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:27.330 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:27.331 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:29.831 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:29.835 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:29.835 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:29.836 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:31.899 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:31.905 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:31.906 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:31.907 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:33.960 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:33.965 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:33.965 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:33.966 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:35.992 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:35.996 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:35.997 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:35.998 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:38.365 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:38.369 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:38.370 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:38.370 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:41.571 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:41.577 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:41.578 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:41.578 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:43.615 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:43.619 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:43.620 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:43.621 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:46.179 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:46.184 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:46.184 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:46.185 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:48.654 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:48.657 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:48.658 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:48.658 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:50.813 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:50.819 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:50.820 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:50.820 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:52.882 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:52.887 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:52.888 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:52.888 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:54.979 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:54.982 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:54.982 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:54.983 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:03.062 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:03.066 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:03.067 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:03.067 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:09.556 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:09.560 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:09.561 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:09.561 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:11.919 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:11.924 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:11.926 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:11.926 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:13.920 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:13.924 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:13.925 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:13.925 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:15.994 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:15.998 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:15.999 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:15.999 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:18.401 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:18.407 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:18.408 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:18.408 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:20.514 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:20.519 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:20.519 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:20.520 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:22.547 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:22.551 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:22.553 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:22.553 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:24.615 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:24.620 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:24.621 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:24.622 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:26.639 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:26.643 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:26.644 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:26.645 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:32.456 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:32.459 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:32.460 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:32.460 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:34.539 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:34.542 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:34.543 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:34.544 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:36.644 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:36.648 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:36.649 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:36.650 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:41.631 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:41.638 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:41.639 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:41.639 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:46.760 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:46.765 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:46.765 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:46.766 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:54.165 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:54.169 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:54.170 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:54.170 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:58.150 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:58.154 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:58.155 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:58.155 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:05.321 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:05.348 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:05.349 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:05.350 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:09.369 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:09.375 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:09.376 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:09.377 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:14.197 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:14.201 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:14.202 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:14.202 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:16.231 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:16.234 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:16.236 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:16.237 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:17:54.506 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:17:54.624 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:17:54.629 [info] index finished after resolve  [object Object] 
2025-07-31 15:17:54.630 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:18:02.778 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-31 15:18:03.027 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-31 15:18:03.030 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-31 15:18:03.033 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-31 15:18:03.038 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-31 15:19:08.447 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:19:08.463 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:19:08.470 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:08.471 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:10.597 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:19:10.608 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:19:10.610 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:10.610 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:15.278 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:19:15.290 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:19:15.291 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:15.291 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:53.241 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:19:53.246 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:19:53.247 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:53.247 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:55.362 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:19:55.366 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:19:55.367 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:55.367 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:06.533 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:06.536 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:06.538 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:06.538 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:08.625 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:08.629 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:08.630 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:08.630 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:11.877 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:11.882 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:11.883 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:11.883 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:14.040 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:14.047 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:14.047 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:14.048 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:21.446 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:21.450 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:21.451 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:21.452 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:23.950 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:23.955 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:23.957 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:23.957 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:28.076 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:28.080 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:28.081 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:28.081 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:30.197 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:30.201 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:30.203 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:30.203 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:33.560 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:33.565 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:33.566 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:33.566 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:35.807 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:35.811 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:35.812 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:35.812 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:37.991 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:37.994 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:37.995 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:37.995 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:56.837 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:56.842 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:56.843 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:56.844 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:59.690 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:59.694 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:59.695 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:59.695 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:03.414 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:03.469 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:03.470 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:03.471 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:07.526 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:07.582 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:07.584 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:07.585 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:20.037 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:20.041 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:20.043 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:20.043 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:23.258 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:23.262 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:23.263 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:23.264 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:32.923 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:32.927 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:32.928 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:32.929 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:46.734 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:46.739 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:46.740 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:46.741 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:53.580 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:53.585 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:53.586 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:53.587 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:56.929 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:56.934 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:56.936 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:56.936 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:00.600 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:00.605 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:00.607 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:00.607 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:03.863 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:03.867 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:03.868 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:03.869 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:05.955 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:05.960 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:05.960 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:05.961 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:08.021 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:08.025 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:08.026 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:08.027 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:20.910 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:21.088 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:21.089 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:21.090 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:23.484 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:23.493 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:23.494 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:23.494 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:26.597 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:26.608 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:26.609 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:26.610 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:31.435 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:31.443 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:31.443 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:31.444 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:39.631 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:39.633 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:39.634 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:39.635 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:53.897 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:53.904 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:53.905 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:53.906 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:58.049 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:58.056 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:58.058 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:58.059 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:24:00.197 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:24:00.204 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:24:00.205 [info] index finished after resolve  [object Object] 
2025-07-31 15:24:00.205 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:24:08.775 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:24:08.778 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:24:08.779 [info] index finished after resolve  [object Object] 
2025-07-31 15:24:08.779 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:28:30.626 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:28:30.633 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:28:30.635 [info] index finished after resolve  [object Object] 
2025-07-31 15:28:30.635 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:28:33.152 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:28:33.159 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:28:33.160 [info] index finished after resolve  [object Object] 
2025-07-31 15:28:33.160 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:28:35.632 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:28:35.640 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:28:35.640 [info] index finished after resolve  [object Object] 
2025-07-31 15:28:35.641 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:28:41.927 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:28:41.934 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:28:41.935 [info] index finished after resolve  [object Object] 
2025-07-31 15:28:41.935 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:28:46.503 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:28:46.510 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:28:46.510 [info] index finished after resolve  [object Object] 
2025-07-31 15:28:46.511 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:30:40.202 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:30:40.207 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:30:40.208 [info] index finished after resolve  [object Object] 
2025-07-31 15:30:40.208 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:30:43.441 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:30:43.444 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:30:43.445 [info] index finished after resolve  [object Object] 
2025-07-31 15:30:43.446 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:30:47.591 [info] components database created cost 1 ms   
2025-07-31 15:30:47.591 [info] components index initializing...   
2025-07-31 15:30:47.863 [info] start to batch put pages: 6   
2025-07-31 15:30:47.897 [info] batch persist cost 6  34 
2025-07-31 15:30:47.942 [info] components index initialized, 1017 files cost 352 ms   
2025-07-31 15:30:47.942 [info] refresh page data from init listeners 0 1017   
2025-07-31 15:30:49.422 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-31 15:30:49.627 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-31 15:30:50.041 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-31 15:30:50.046 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-31 15:30:50.056 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-31 15:30:50.060 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-31 15:31:02.740 [debug] ignore file modify evnet 工作库/项目/舌诊/SCI.md   
2025-07-31 15:31:02.747 [info] trigger 工作库/项目/舌诊/SCI.md resolve  [object Object] 
2025-07-31 15:31:02.749 [info] index finished after resolve  [object Object] 
2025-07-31 15:31:02.750 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:31:09.403 [debug] ignore file modify evnet 工作库/项目/舌诊/SCI.md   
2025-07-31 15:31:09.420 [info] trigger 工作库/项目/舌诊/SCI.md resolve  [object Object] 
2025-07-31 15:31:09.420 [info] index finished after resolve  [object Object] 
2025-07-31 15:31:09.421 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:05.912 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:33:06.041 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:33:06.043 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:06.043 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:07.998 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:33:08.136 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:33:08.138 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:08.140 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:10.090 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:33:10.096 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:33:10.096 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:10.097 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:12.228 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:33:12.377 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:33:12.380 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:12.381 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:14.324 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:33:14.329 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:33:14.330 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:14.331 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:17.898 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:33:17.977 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:33:17.978 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:17.978 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:52.317 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:33:52.368 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:33:52.372 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:52.373 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:33:54.956 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:33:54.966 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:33:54.967 [info] index finished after resolve  [object Object] 
2025-07-31 15:33:54.968 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:34:06.189 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:34:06.198 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:34:06.198 [info] index finished after resolve  [object Object] 
2025-07-31 15:34:06.199 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:34:15.612 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:34:15.620 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:34:15.623 [info] index finished after resolve  [object Object] 
2025-07-31 15:34:15.624 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:10.836 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:10.844 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:10.845 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:10.846 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:13.142 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:13.151 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:13.151 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:13.152 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:22.455 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:22.466 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:22.468 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:22.468 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:25.167 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:25.174 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:25.175 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:25.175 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:27.317 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:27.326 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:27.327 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:27.327 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:32.615 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:32.623 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:32.624 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:32.625 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:38.358 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:38.403 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:38.404 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:38.405 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:39.883 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:39.891 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:39.892 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:39.893 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:49.340 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:49.454 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:49.456 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:49.457 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:52.891 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:52.901 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:52.902 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:52.903 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:56.226 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:56.233 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:56.235 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:56.235 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:42:58.728 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:42:58.735 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:42:58.736 [info] index finished after resolve  [object Object] 
2025-07-31 15:42:58.737 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:43:15.835 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:43:15.842 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:43:15.842 [info] index finished after resolve  [object Object] 
2025-07-31 15:43:15.843 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:43:20.072 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:43:20.080 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:43:20.081 [info] index finished after resolve  [object Object] 
2025-07-31 15:43:20.082 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:43:23.318 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:43:23.327 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:43:23.329 [info] index finished after resolve  [object Object] 
2025-07-31 15:43:23.329 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:43:30.645 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:43:30.654 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:43:30.655 [info] index finished after resolve  [object Object] 
2025-07-31 15:43:30.656 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:43:34.757 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:43:34.765 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:43:34.765 [info] index finished after resolve  [object Object] 
2025-07-31 15:43:34.766 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:43:39.945 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:43:39.953 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:43:39.954 [info] index finished after resolve  [object Object] 
2025-07-31 15:43:39.955 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:06.593 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:06.639 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:06.641 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:06.641 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:11.296 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:11.303 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:11.305 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:11.305 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:13.685 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:13.692 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:13.694 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:13.695 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:16.070 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:16.077 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:16.078 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:16.079 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:18.336 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:18.342 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:18.343 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:18.344 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:27.895 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:27.905 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:27.906 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:27.907 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:30.100 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:30.107 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:30.109 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:30.109 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:32.219 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:32.228 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:32.229 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:32.230 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:34.417 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:34.426 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:34.427 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:34.427 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:36.523 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:36.533 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:36.536 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:36.536 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:40.443 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:40.450 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:40.451 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:40.452 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:44.083 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:44.090 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:44.091 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:44.092 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:49.103 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:49.146 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:49.147 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:49.148 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:44:53.698 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:44:53.706 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:44:53.708 [info] index finished after resolve  [object Object] 
2025-07-31 15:44:53.709 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:03.470 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:03.478 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:03.479 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:03.480 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:05.607 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:05.610 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:05.623 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:05.624 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:07.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:07.634 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:07.634 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:07.635 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:14.722 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:14.766 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:14.767 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:14.767 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:19.371 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:19.378 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:19.378 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:19.379 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:23.196 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:23.204 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:23.206 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:23.206 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:25.673 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:25.679 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:25.680 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:25.681 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:35.999 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:36.004 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:36.005 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:36.006 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:42.689 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:42.697 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:42.698 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:42.699 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:44.688 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:44.694 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:44.695 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:44.696 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:55.069 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:55.076 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:55.077 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:55.078 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:57.177 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:57.188 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:57.189 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:57.190 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:45:59.179 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:45:59.185 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:45:59.187 [info] index finished after resolve  [object Object] 
2025-07-31 15:45:59.187 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:02.055 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:02.061 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:02.062 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:02.063 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:17.317 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:17.323 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:17.324 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:17.325 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:34.270 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:34.277 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:34.278 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:34.279 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:36.569 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:36.576 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:36.577 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:36.577 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:40.277 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:40.282 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:40.283 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:40.284 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:43.045 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:43.052 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:43.053 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:43.054 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:45.112 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:45.121 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:45.122 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:45.122 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:49.354 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:49.360 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:49.361 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:49.362 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:52.622 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:52.629 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:52.630 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:52.630 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:46:58.612 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:46:58.659 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:46:58.660 [info] index finished after resolve  [object Object] 
2025-07-31 15:46:58.660 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:02.771 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:02.776 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:02.777 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:02.778 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:05.550 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:05.557 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:05.558 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:05.559 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:13.836 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:13.887 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:13.889 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:13.890 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:18.757 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:18.763 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:18.764 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:18.765 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:32.921 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:32.927 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:32.928 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:32.929 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:35.256 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:35.262 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:35.263 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:35.264 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:37.307 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:37.313 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:37.314 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:37.315 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:47:44.809 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:47:44.813 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:47:44.814 [info] index finished after resolve  [object Object] 
2025-07-31 15:47:44.815 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:01.691 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:01.698 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:01.699 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:01.700 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:22.015 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:22.021 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:22.022 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:22.023 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:24.550 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:24.557 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:24.558 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:24.559 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:26.848 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:26.855 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:26.856 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:26.857 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:29.259 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:29.264 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:29.265 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:29.266 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:31.428 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:31.435 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:31.435 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:31.436 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:33.547 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:33.551 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:33.552 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:33.553 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:35.831 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:35.837 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:35.839 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:35.839 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:38.086 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:38.092 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:38.093 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:38.094 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:41.568 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:41.574 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:41.575 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:41.576 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:43.689 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:43.695 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:43.696 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:43.697 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:46.867 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:46.873 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:46.875 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:46.875 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:49.293 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:49.300 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:49.301 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:49.301 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:52.422 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:52.429 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:52.430 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:52.431 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:48:57.720 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:48:57.728 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:48:57.728 [info] index finished after resolve  [object Object] 
2025-07-31 15:48:57.729 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:49:01.256 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:49:01.301 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:49:01.302 [info] index finished after resolve  [object Object] 
2025-07-31 15:49:01.303 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:49:06.379 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:49:06.385 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:49:06.387 [info] index finished after resolve  [object Object] 
2025-07-31 15:49:06.388 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:50:02.207 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:50:02.213 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:50:02.215 [info] index finished after resolve  [object Object] 
2025-07-31 15:50:02.215 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:50:04.831 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:50:04.836 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:50:04.837 [info] index finished after resolve  [object Object] 
2025-07-31 15:50:04.837 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:50:07.261 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:50:07.266 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:50:07.268 [info] index finished after resolve  [object Object] 
2025-07-31 15:50:07.268 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:50:29.541 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:50:29.547 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:50:29.548 [info] index finished after resolve  [object Object] 
2025-07-31 15:50:29.548 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:50:44.046 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:50:44.052 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:50:44.053 [info] index finished after resolve  [object Object] 
2025-07-31 15:50:44.053 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:03.404 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:03.412 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:03.412 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:03.413 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:05.695 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:05.700 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:05.701 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:05.702 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:16.913 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:16.919 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:16.920 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:16.921 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:18.880 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:18.883 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:18.884 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:18.885 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:22.592 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:22.598 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:22.599 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:22.599 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:38.672 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:38.680 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:38.682 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:38.682 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:40.818 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:40.824 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:40.825 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:40.825 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:43.078 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:43.084 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:43.085 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:43.085 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:48.416 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:48.419 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:48.420 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:48.421 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:50.665 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:50.671 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:50.671 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:50.672 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:53.865 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:53.870 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:53.872 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:53.872 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:51:57.826 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:51:57.833 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:51:57.834 [info] index finished after resolve  [object Object] 
2025-07-31 15:51:57.835 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:52:18.395 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:52:18.445 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:52:18.446 [info] index finished after resolve  [object Object] 
2025-07-31 15:52:18.447 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:52:25.640 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:52:25.647 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:52:25.648 [info] index finished after resolve  [object Object] 
2025-07-31 15:52:25.649 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:52:28.456 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:52:28.463 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:52:28.464 [info] index finished after resolve  [object Object] 
2025-07-31 15:52:28.464 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:52:33.907 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:52:33.913 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:52:33.914 [info] index finished after resolve  [object Object] 
2025-07-31 15:52:33.914 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:52:35.955 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:52:35.962 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:52:35.964 [info] index finished after resolve  [object Object] 
2025-07-31 15:52:35.964 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:52:39.127 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:52:39.133 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:52:39.133 [info] index finished after resolve  [object Object] 
2025-07-31 15:52:39.134 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:13.721 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:13.726 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:13.727 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:13.728 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:23.219 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:23.225 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:23.226 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:23.227 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:25.436 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:25.441 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:25.443 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:25.443 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:28.271 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:28.278 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:28.278 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:28.279 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:36.253 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:36.259 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:36.259 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:36.260 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:46.320 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:46.391 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:46.392 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:46.393 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:54.792 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:54.799 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:54.800 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:54.801 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:53:57.916 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:53:57.922 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:53:57.923 [info] index finished after resolve  [object Object] 
2025-07-31 15:53:57.924 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:21.617 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:21.625 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:21.626 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:21.626 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:23.730 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:23.736 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:23.737 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:23.738 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:26.140 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:26.146 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:26.147 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:26.148 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:37.346 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:37.352 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:37.353 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:37.354 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:39.507 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:39.521 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:39.522 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:39.523 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:41.522 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:41.530 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:41.531 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:41.531 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:44.366 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:44.371 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:44.371 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:44.372 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:46.864 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:46.870 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:46.872 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:46.873 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:48.902 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:48.909 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:48.910 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:48.911 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:51.305 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:51.313 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:51.314 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:51.314 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:53.515 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:53.520 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:53.522 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:53.522 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:55:55.544 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:55:55.550 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:55:55.551 [info] index finished after resolve  [object Object] 
2025-07-31 15:55:55.551 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:02.064 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:56:02.077 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:56:02.078 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:02.078 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:12.522 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:56:12.528 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:56:12.529 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:12.530 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:14.674 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:56:14.677 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:56:14.678 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:14.679 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:26.479 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:56:26.486 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:56:26.486 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:26.487 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:45.968 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:56:45.972 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:56:45.973 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:45.974 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:48.336 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:56:48.340 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:56:48.342 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:48.343 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:56:58.667 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:56:58.671 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:56:58.672 [info] index finished after resolve  [object Object] 
2025-07-31 15:56:58.672 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:01.257 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:01.260 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:01.261 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:01.262 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:03.304 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:03.307 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:03.308 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:03.309 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:05.972 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:05.976 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:05.977 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:05.977 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:10.240 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:10.245 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:10.246 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:10.247 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:13.261 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:13.266 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:13.267 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:13.267 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:15.439 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:15.443 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:15.443 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:15.444 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:17.517 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:17.521 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:17.522 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:17.523 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:19.594 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:19.599 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:19.600 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:19.601 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:21.626 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:21.630 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:21.630 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:21.631 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:24.057 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:24.060 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:24.061 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:24.061 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:26.243 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:26.248 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:26.248 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:26.249 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:28.465 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:28.471 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:28.472 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:28.472 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:30.725 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:30.730 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:30.731 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:30.731 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:57:32.779 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:57:32.783 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:57:32.783 [info] index finished after resolve  [object Object] 
2025-07-31 15:57:32.784 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:18.032 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:18.037 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:18.038 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:18.039 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:20.153 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:20.157 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:20.159 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:20.159 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:22.634 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:22.639 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:22.639 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:22.640 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:24.708 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:24.713 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:24.715 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:24.715 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:30.689 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:30.693 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:30.694 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:30.694 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:32.856 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:32.861 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:32.862 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:32.863 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:35.940 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:35.945 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:35.946 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:35.947 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:39.671 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:39.676 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:39.677 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:39.678 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:43.465 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:43.468 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:43.469 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:43.470 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:58:46.770 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-31 15:58:46.964 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-31 15:58:46.972 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-31 15:58:46.977 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-31 15:58:47.026 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-31 15:58:47.158 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-31 15:58:47.248 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-31 15:58:47.252 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-31 15:58:47.256 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-31 15:58:47.260 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-31 15:58:59.084 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:58:59.088 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:58:59.089 [info] index finished after resolve  [object Object] 
2025-07-31 15:58:59.090 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:59:01.316 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:59:01.323 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:59:01.324 [info] index finished after resolve  [object Object] 
2025-07-31 15:59:01.325 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:59:03.012 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:59:03.016 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:59:03.092 [info] index finished after resolve  [object Object] 
2025-07-31 15:59:03.092 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:07:15.115 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:07:15.123 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:07:15.123 [info] index finished after resolve  [object Object] 
2025-07-31 16:07:15.124 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:07:17.552 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:07:17.561 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:07:17.562 [info] index finished after resolve  [object Object] 
2025-07-31 16:07:17.563 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:11:28.350 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:11:28.358 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:11:28.360 [info] index finished after resolve  [object Object] 
2025-07-31 16:11:28.360 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:08.733 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:08.824 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:08.826 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:08.827 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:11.522 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:11.530 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:11.531 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:11.531 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:15.253 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:15.261 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:15.262 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:15.262 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:17.337 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:17.343 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:17.360 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:17.361 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:21.299 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:21.340 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:21.343 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:21.343 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:24.092 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:24.099 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:24.100 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:24.101 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:30.255 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:30.369 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:30.372 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:30.373 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:33.141 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:33.149 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:33.150 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:33.150 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:35.606 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:35.612 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:35.613 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:35.613 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:37.932 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:37.939 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:37.939 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:37.940 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:42.903 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:43.021 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:43.022 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:43.022 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:51.349 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:51.355 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:51.356 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:51.357 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:28:54.829 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:28:54.835 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:28:54.835 [info] index finished after resolve  [object Object] 
2025-07-31 16:28:54.836 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:05.267 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:05.292 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:05.293 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:05.293 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:08.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:08.633 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:08.634 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:08.634 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:10.893 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:10.900 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:10.901 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:10.902 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:22.145 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:22.236 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:22.239 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:22.240 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:27.764 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:27.770 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:27.771 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:27.772 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:29.914 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:30.003 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:30.005 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:30.006 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:32.580 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:32.588 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:32.589 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:32.589 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:34.923 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:34.930 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:34.931 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:34.931 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:37.637 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:37.639 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:37.641 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:37.641 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:39.718 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:39.724 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:39.725 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:39.726 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:41.749 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:41.755 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:41.756 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:41.757 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:29:43.874 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:29:43.880 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:29:43.881 [info] index finished after resolve  [object Object] 
2025-07-31 16:29:43.882 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:08.779 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:08.785 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:08.786 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:08.787 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:10.864 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:10.871 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:10.872 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:10.873 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:14.685 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:14.785 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:14.788 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:14.788 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:17.133 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:17.231 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:17.233 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:17.234 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:20.067 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:20.172 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:20.177 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:20.179 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:22.373 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:22.460 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:22.463 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:22.464 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:25.633 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:25.720 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:25.722 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:25.723 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:28.368 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:28.453 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:28.456 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:28.456 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:30.746 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:30.753 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:30.754 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:30.754 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:33.610 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:33.708 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:33.709 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:33.710 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:35.771 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:35.874 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:35.876 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:35.876 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:39.386 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:39.392 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:39.395 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:39.395 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:30:53.174 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:30:53.181 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:30:53.182 [info] index finished after resolve  [object Object] 
2025-07-31 16:30:53.183 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:12.257 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:12.359 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:12.361 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:12.362 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:14.684 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:14.690 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:14.691 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:14.691 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:23.360 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:23.496 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:23.521 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:23.521 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:28.207 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:28.313 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:28.314 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:28.315 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:30.904 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:30.911 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:30.912 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:30.912 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:35.529 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:35.534 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:35.535 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:35.536 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:41.820 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:41.928 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:41.930 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:41.931 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:31:44.639 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:31:44.645 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:31:44.647 [info] index finished after resolve  [object Object] 
2025-07-31 16:31:44.647 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:04.583 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:04.589 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:04.590 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:04.590 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:06.650 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:06.656 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:06.656 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:06.657 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:10.884 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:10.889 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:10.890 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:10.891 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:25.941 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:26.029 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:26.030 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:26.030 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:28.119 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:28.213 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:28.214 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:28.215 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:33.752 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:33.842 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:33.845 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:33.846 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:40.050 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:40.145 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:40.147 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:40.148 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:42.197 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:42.204 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:42.206 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:42.206 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:32:44.292 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:32:44.301 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:32:44.302 [info] index finished after resolve  [object Object] 
2025-07-31 16:32:44.302 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:31.175 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:31.284 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:31.288 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:31.289 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:33.959 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:33.966 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:33.968 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:33.969 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:38.121 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:38.128 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:38.129 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:38.129 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:41.389 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:41.513 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:41.515 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:41.516 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:43.504 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:43.511 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:43.512 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:43.513 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:45.817 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:45.941 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:45.946 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:45.950 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:50.062 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:50.170 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:50.173 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:50.174 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:33:52.591 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:33:52.597 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:33:52.599 [info] index finished after resolve  [object Object] 
2025-07-31 16:33:52.599 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:36:25.812 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:36:25.820 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:36:25.822 [info] index finished after resolve  [object Object] 
2025-07-31 16:36:25.823 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:37:58.009 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:37:58.139 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:37:58.147 [info] index finished after resolve  [object Object] 
2025-07-31 16:37:58.148 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:00.128 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:00.134 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:00.136 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:00.137 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:02.484 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:02.491 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:02.493 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:02.493 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:04.610 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:04.616 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:04.617 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:04.618 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:06.668 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:06.674 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:06.675 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:06.676 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:11.733 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:11.757 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:11.759 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:11.760 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:13.755 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:13.761 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:13.764 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:13.765 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:16.184 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:16.190 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:16.192 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:16.193 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:18.956 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:18.963 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:18.963 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:18.964 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:27.808 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:27.814 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:27.815 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:27.816 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:38:59.029 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:38:59.036 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:38:59.040 [info] index finished after resolve  [object Object] 
2025-07-31 16:38:59.041 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:01.617 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:01.623 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:01.626 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:01.626 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:04.236 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:04.245 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:04.246 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:04.247 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:08.244 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:08.249 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:08.250 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:08.250 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:10.701 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:10.707 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:10.710 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:10.711 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:12.764 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:12.770 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:12.771 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:12.772 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:14.871 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:14.878 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:14.881 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:14.882 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:19.570 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:19.577 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:19.579 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:19.580 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:36.494 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:36.502 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:36.503 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:36.504 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:38.663 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:38.672 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:38.675 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:38.675 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:40.920 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:40.928 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:40.931 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:40.931 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:44.890 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:44.897 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:44.899 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:44.900 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:48.087 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:48.093 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:48.095 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:48.096 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:50.195 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:50.203 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:50.204 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:50.204 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:39:56.039 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:39:56.146 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:39:56.149 [info] index finished after resolve  [object Object] 
2025-07-31 16:39:56.150 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:40:01.715 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:40:01.721 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:40:01.724 [info] index finished after resolve  [object Object] 
2025-07-31 16:40:01.724 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:40:22.544 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:40:22.549 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:40:22.550 [info] index finished after resolve  [object Object] 
2025-07-31 16:40:22.551 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:40:26.797 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:40:26.807 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:40:26.809 [info] index finished after resolve  [object Object] 
2025-07-31 16:40:26.809 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:40:33.479 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:40:33.587 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:40:33.587 [info] index finished after resolve  [object Object] 
2025-07-31 16:40:33.588 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:40:58.476 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:40:58.585 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:40:58.592 [info] index finished after resolve  [object Object] 
2025-07-31 16:40:58.593 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:00.733 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:00.739 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:00.740 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:00.741 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:03.660 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:03.774 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:03.778 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:03.779 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:05.883 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:05.890 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:05.890 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:05.891 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:08.908 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:08.916 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:08.917 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:08.918 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:11.180 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:11.185 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:11.186 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:11.187 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:13.217 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:13.224 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:13.226 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:13.227 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:40.093 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:40.100 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:40.101 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:40.101 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:42.265 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:42.271 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:42.273 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:42.274 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:44.311 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:44.318 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:44.321 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:44.322 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:53.290 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:53.297 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:53.298 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:53.298 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:55.367 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:55.373 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:55.376 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:55.376 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:41:57.758 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:41:57.765 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:41:57.767 [info] index finished after resolve  [object Object] 
2025-07-31 16:41:57.768 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:01.257 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:01.264 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:01.266 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:01.267 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:06.855 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:06.861 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:06.864 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:06.865 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:09.207 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:09.323 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:09.326 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:09.327 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:11.843 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:11.973 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:11.974 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:11.975 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:13.965 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:13.970 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:13.973 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:13.974 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:16.354 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:16.360 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:16.360 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:16.361 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:18.689 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:18.696 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:18.697 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:18.697 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:42:21.046 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:42:21.159 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:42:21.161 [info] index finished after resolve  [object Object] 
2025-07-31 16:42:21.162 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:43:51.998 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:43:52.006 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:43:52.008 [info] index finished after resolve  [object Object] 
2025-07-31 16:43:52.009 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:43:54.081 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:43:54.087 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:43:54.090 [info] index finished after resolve  [object Object] 
2025-07-31 16:43:54.090 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:43:56.113 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:43:56.121 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:43:56.125 [info] index finished after resolve  [object Object] 
2025-07-31 16:43:56.125 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:43:58.351 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:43:58.357 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:43:58.359 [info] index finished after resolve  [object Object] 
2025-07-31 16:43:58.360 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:00.401 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:00.406 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:00.408 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:00.409 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:02.499 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:02.505 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:02.508 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:02.509 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:04.539 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:04.546 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:04.547 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:04.547 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:12.094 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:12.101 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:12.105 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:12.105 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:14.207 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:14.213 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:14.217 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:14.217 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:16.207 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:16.214 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:16.216 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:16.217 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:18.977 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:18.986 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:18.989 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:18.990 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:21.093 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:21.100 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:21.101 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:21.101 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:23.227 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:23.234 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:23.236 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:23.237 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:26.993 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:27.001 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:27.003 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:27.004 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:29.044 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:29.051 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:29.053 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:29.054 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:31.155 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:31.161 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:31.165 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:31.166 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:33.193 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:33.199 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:33.202 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:33.202 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:35.257 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:35.263 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:35.265 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:35.266 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:37.958 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:37.965 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:37.967 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:37.968 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:40.041 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:40.046 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:40.049 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:40.049 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:42.127 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:42.134 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:42.137 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:42.137 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:44.312 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:44.319 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:44.319 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:44.320 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:48.406 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:48.412 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:48.412 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:48.413 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:51.510 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:51.517 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:51.518 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:51.518 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:54.114 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:54.120 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:54.123 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:54.124 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:56.145 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:56.152 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:56.154 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:56.154 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:44:58.271 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:44:58.278 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:44:58.280 [info] index finished after resolve  [object Object] 
2025-07-31 16:44:58.281 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:00.384 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:00.391 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:00.391 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:00.392 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:02.498 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:02.505 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:02.506 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:02.506 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:04.671 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:04.677 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:04.678 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:04.679 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:08.820 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:08.827 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:08.828 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:08.829 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:13.935 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:13.941 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:13.942 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:13.942 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:16.942 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:16.950 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:16.953 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:16.954 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:23.744 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:23.751 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:23.752 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:23.753 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:26.882 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:26.889 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:26.891 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:26.892 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:29.078 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:29.201 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:29.203 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:29.204 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:31.289 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:31.294 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:31.295 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:31.295 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:33.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:33.633 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:33.635 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:33.636 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:35.834 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:35.841 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:35.846 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:35.847 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:40.036 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:40.042 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:40.042 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:40.043 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:42.025 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:42.032 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:42.035 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:42.035 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:45.599 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:45.605 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:45.608 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:45.608 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:47.632 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:47.638 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:47.641 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:47.642 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:49.692 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:49.698 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:49.702 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:49.702 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:51.789 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:51.795 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:51.796 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:51.796 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:57.693 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:57.698 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:57.699 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:57.699 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:45:59.749 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:45:59.755 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:45:59.756 [info] index finished after resolve  [object Object] 
2025-07-31 16:45:59.756 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:07.981 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:07.988 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:07.989 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:07.990 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:16.982 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:16.989 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:16.990 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:16.991 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:19.014 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:19.021 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:19.022 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:19.023 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:22.126 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:22.132 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:22.135 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:22.135 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:24.881 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:24.888 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:24.891 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:24.892 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:26.909 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:26.916 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:26.918 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:26.919 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:30.076 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:30.083 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:30.086 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:30.086 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:34.333 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:34.339 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:34.343 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:34.343 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:36.713 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:36.721 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:36.723 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:36.724 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:38.791 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:38.797 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:38.798 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:38.799 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:40.819 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:40.826 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:40.829 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:40.829 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:43.822 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:43.830 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:43.831 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:43.831 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:45.954 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:45.961 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:45.963 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:45.964 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:52.557 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:52.564 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:52.567 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:52.568 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:46:57.135 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:46:57.143 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:46:57.144 [info] index finished after resolve  [object Object] 
2025-07-31 16:46:57.145 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:03.443 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:03.450 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:03.453 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:03.453 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:12.037 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:12.043 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:12.044 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:12.044 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:14.100 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:14.107 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:14.107 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:14.108 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:16.350 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:16.356 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:16.358 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:16.359 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:19.784 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:19.791 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:19.793 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:19.793 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:22.226 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:22.233 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:22.237 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:22.238 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:24.316 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:24.326 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:24.328 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:24.329 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:27.733 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:27.739 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:27.742 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:27.743 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:29.998 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:30.004 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:30.007 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:30.008 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:32.006 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:32.239 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:32.266 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:32.267 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:47:51.545 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:47:51.550 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:47:51.551 [info] index finished after resolve  [object Object] 
2025-07-31 16:47:51.552 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:00.705 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:00.749 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:00.750 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:00.751 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:03.890 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:04.015 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:04.017 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:04.018 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:08.927 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:09.057 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:09.065 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:09.066 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:11.043 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:11.050 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:11.054 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:11.054 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:13.629 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:13.636 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:13.639 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:13.640 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:22.245 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:22.366 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:22.368 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:22.370 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:25.752 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:25.860 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:25.863 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:25.864 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:28.566 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:28.573 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:28.574 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:28.575 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:36.049 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:36.056 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:36.057 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:36.057 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:48:48.900 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:48:49.016 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:48:49.017 [info] index finished after resolve  [object Object] 
2025-07-31 16:48:49.018 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:49:12.734 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:49:12.839 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:49:12.842 [info] index finished after resolve  [object Object] 
2025-07-31 16:49:12.843 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:49:57.343 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:49:57.348 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:49:57.352 [info] index finished after resolve  [object Object] 
2025-07-31 16:49:57.352 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:49:59.419 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:49:59.425 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:49:59.428 [info] index finished after resolve  [object Object] 
2025-07-31 16:49:59.428 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:01.484 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:01.492 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:01.492 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:01.493 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:04.219 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:04.226 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:04.229 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:04.229 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:06.338 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:06.345 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:06.347 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:06.347 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:08.498 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:08.504 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:08.507 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:08.508 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:10.617 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:10.623 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:10.624 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:10.625 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:12.713 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:12.719 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:12.723 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:12.723 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:14.812 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:14.820 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:14.822 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:14.823 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:16.992 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:16.998 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:16.999 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:16.999 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:22.141 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:22.147 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:22.153 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:22.154 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:24.336 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:24.342 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:24.345 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:24.346 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:27.011 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:27.150 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:27.152 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:27.154 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:29.415 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:29.421 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:29.424 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:29.425 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:36.483 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:36.491 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:36.492 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:36.493 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:41.917 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:42.031 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:42.033 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:42.033 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:50:46.179 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:50:46.297 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:50:46.309 [info] index finished after resolve  [object Object] 
2025-07-31 16:50:46.310 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:53:52.567 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:53:52.574 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:53:52.579 [info] index finished after resolve  [object Object] 
2025-07-31 16:53:52.580 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:53:55.241 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:53:55.247 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:53:55.252 [info] index finished after resolve  [object Object] 
2025-07-31 16:53:55.253 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:53:57.437 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:53:57.444 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:53:57.446 [info] index finished after resolve  [object Object] 
2025-07-31 16:53:57.447 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:53:59.840 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:53:59.847 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:53:59.849 [info] index finished after resolve  [object Object] 
2025-07-31 16:53:59.850 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:54:02.810 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:54:02.817 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:54:02.818 [info] index finished after resolve  [object Object] 
2025-07-31 16:54:02.818 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:54:05.187 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:54:05.193 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:54:05.196 [info] index finished after resolve  [object Object] 
2025-07-31 16:54:05.197 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:54:41.130 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:54:41.244 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:54:41.252 [info] index finished after resolve  [object Object] 
2025-07-31 16:54:41.253 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:54:47.259 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:54:47.267 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:54:47.268 [info] index finished after resolve  [object Object] 
2025-07-31 16:54:47.269 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:54:57.043 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:54:57.049 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:54:57.051 [info] index finished after resolve  [object Object] 
2025-07-31 16:54:57.052 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:55:01.168 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:55:01.174 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:55:01.175 [info] index finished after resolve  [object Object] 
2025-07-31 16:55:01.176 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:55:05.286 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:55:05.433 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:55:05.436 [info] index finished after resolve  [object Object] 
2025-07-31 16:55:05.437 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:55:35.708 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:55:35.713 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:55:35.720 [info] index finished after resolve  [object Object] 
2025-07-31 16:55:35.720 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:55:40.964 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:55:40.969 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:55:40.970 [info] index finished after resolve  [object Object] 
2025-07-31 16:55:40.971 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:02.952 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:02.957 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:02.961 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:02.962 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:06.540 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:06.542 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:06.545 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:06.545 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:17.451 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:17.458 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:17.459 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:17.460 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:19.716 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:19.724 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:19.725 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:19.726 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:22.341 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:22.344 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:22.345 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:22.345 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:26.173 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:26.182 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:26.183 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:26.183 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:28.924 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:28.932 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:28.934 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:28.935 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:50.369 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:50.374 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:50.380 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:50.381 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:54.443 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:54.452 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:54.453 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:54.454 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:56.974 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:56.982 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:56.984 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:56.985 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:56:59.544 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:56:59.550 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:56:59.552 [info] index finished after resolve  [object Object] 
2025-07-31 16:56:59.552 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:02.633 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:02.839 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:02.839 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:02.840 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:06.874 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:06.879 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:06.884 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:06.885 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:15.600 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:15.607 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:15.610 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:15.611 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:17.934 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:17.939 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:17.945 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:17.945 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:20.014 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:20.021 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:20.024 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:20.025 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:28.772 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:28.778 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:28.781 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:28.781 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:30.814 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:30.822 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:30.823 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:30.823 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:32.910 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:32.917 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:32.919 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:32.920 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:35.453 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:35.459 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:35.461 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:35.462 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:37.544 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:37.553 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:37.555 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:37.555 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:47.793 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:47.799 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:47.801 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:47.802 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:57.377 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:57.384 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:57.386 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:57.387 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:58:59.780 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:58:59.787 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:58:59.789 [info] index finished after resolve  [object Object] 
2025-07-31 16:58:59.790 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:02.145 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:02.151 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:02.153 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:02.154 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:05.154 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:05.160 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:05.161 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:05.162 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:07.749 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:07.755 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:07.756 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:07.757 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:10.663 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:10.670 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:10.671 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:10.672 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:12.717 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:12.725 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:12.728 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:12.729 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:14.953 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:14.959 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:14.960 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:14.961 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:17.802 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:17.809 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:17.811 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:17.812 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:20.962 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:20.969 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:20.972 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:20.973 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:23.023 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:23.030 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:23.031 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:23.032 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:25.024 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:25.030 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:25.031 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:25.032 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:28.211 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:28.219 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:28.220 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:28.221 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:30.841 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:30.848 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:30.851 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:30.852 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:48.620 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:48.625 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:48.631 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:48.632 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 16:59:58.329 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 16:59:58.338 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 16:59:58.341 [info] index finished after resolve  [object Object] 
2025-07-31 16:59:58.342 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:09.821 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:09.827 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:09.829 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:09.830 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:14.512 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:14.663 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:14.666 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:14.667 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:24.721 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:24.726 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:24.727 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:24.728 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:26.885 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:26.891 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:26.893 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:26.894 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:29.599 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:29.603 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:29.607 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:29.607 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:55.507 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:55.512 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:55.515 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:55.515 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:00:58.235 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:00:58.244 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:00:58.244 [info] index finished after resolve  [object Object] 
2025-07-31 17:00:58.245 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:06.209 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:06.217 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:06.219 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:06.220 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:16.721 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:16.728 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:16.731 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:16.732 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:18.929 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:18.935 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:18.938 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:18.939 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:22.017 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:22.024 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:22.025 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:22.025 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:26.041 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:26.047 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:26.048 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:26.049 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:30.308 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:30.314 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:30.315 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:30.316 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:35.861 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:35.868 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:35.871 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:35.872 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:38.348 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:38.354 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:38.360 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:38.360 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:41.912 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:41.917 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:41.923 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:41.923 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:01:59.677 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:01:59.683 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:01:59.690 [info] index finished after resolve  [object Object] 
2025-07-31 17:01:59.690 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:07.077 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:07.082 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:07.085 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:07.085 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:15.344 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:15.350 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:15.351 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:15.352 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:19.505 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:19.511 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:19.514 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:19.514 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:40.331 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:40.341 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:40.343 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:40.343 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:42.393 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:42.400 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:42.408 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:42.409 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:44.589 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:44.596 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:44.597 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:44.598 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:46.788 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:46.795 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:46.798 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:46.798 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:49.099 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:49.105 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:49.108 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:49.108 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:51.768 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:51.775 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:51.777 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:51.778 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:54.734 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:54.742 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:54.744 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:54.744 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:02:57.582 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:02:57.589 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:02:57.594 [info] index finished after resolve  [object Object] 
2025-07-31 17:02:57.595 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:02.942 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:02.948 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:02.951 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:02.951 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:09.010 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:09.018 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:09.019 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:09.019 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:11.600 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:11.606 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:11.609 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:11.609 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:13.638 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:13.644 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:13.646 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:13.647 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:15.825 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:15.846 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:15.848 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:15.849 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:17.845 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:17.851 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:17.852 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:17.852 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:20.262 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:20.269 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:20.275 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:20.276 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:22.445 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:22.451 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:22.453 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:22.453 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:38.087 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:38.272 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:38.284 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:38.286 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:51.346 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:51.352 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:51.352 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:51.353 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:54.043 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:54.050 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:54.052 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:54.053 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:03:59.183 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:03:59.190 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:03:59.192 [info] index finished after resolve  [object Object] 
2025-07-31 17:03:59.192 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:08.651 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:08.658 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:08.659 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:08.660 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:16.276 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:16.283 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:16.284 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:16.285 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:27.172 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:27.179 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:27.180 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:27.180 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:31.820 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:31.827 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:31.831 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:31.832 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:34.303 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:34.311 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:34.313 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:34.314 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:45.274 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:45.281 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:45.283 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:45.284 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:51.866 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:51.872 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:51.873 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:51.873 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:04:55.953 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:04:55.961 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:04:55.962 [info] index finished after resolve  [object Object] 
2025-07-31 17:04:55.963 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:02.098 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:02.105 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:02.107 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:02.108 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:05.834 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:05.840 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:05.844 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:05.845 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:08.321 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:08.329 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:08.330 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:08.330 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:12.459 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:12.466 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:12.468 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:12.469 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:19.928 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:19.934 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:19.936 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:19.937 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:37.421 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:37.427 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:37.433 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:37.434 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:39.568 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:39.571 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:39.572 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:39.573 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:05:50.102 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-31 17:05:50.109 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-31 17:05:50.112 [info] index finished after resolve  [object Object] 
2025-07-31 17:05:50.113 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:39.072 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:39.193 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:39.205 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:39.206 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:44.252 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:44.257 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:44.258 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:44.258 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:46.290 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:46.295 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:46.300 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:46.301 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:48.368 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:48.373 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:48.374 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:48.375 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:50.793 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:50.798 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:50.802 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:50.802 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:52.858 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:52.862 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:52.866 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:52.867 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:08:55.244 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:08:55.249 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:08:55.250 [info] index finished after resolve  [object Object] 
2025-07-31 17:08:55.251 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:00.277 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:00.282 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:00.286 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:00.286 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:22.733 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:22.738 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:22.739 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:22.739 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:29.124 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:29.130 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:29.134 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:29.135 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:31.424 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:31.429 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:31.434 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:31.435 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:33.687 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:33.692 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:33.692 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:33.693 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:35.769 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:35.773 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:35.778 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:35.779 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:38.625 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:38.631 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:38.632 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:38.633 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:46.492 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:46.498 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:46.499 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:46.500 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:09:54.194 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:09:54.201 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:09:54.202 [info] index finished after resolve  [object Object] 
2025-07-31 17:09:54.203 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:00.306 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:00.311 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:00.315 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:00.316 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:02.620 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:02.624 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:02.626 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:02.626 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:04.780 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:04.785 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:04.788 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:04.789 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:06.941 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:06.945 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:06.946 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:06.946 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:09.032 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:09.039 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:09.039 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:09.040 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:15.620 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:15.624 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:15.625 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:15.626 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:17.711 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:17.718 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:17.723 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:17.724 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:24.996 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:25.002 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:25.005 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:25.006 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:30.441 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:30.446 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:30.446 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:30.447 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:32.439 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:32.445 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:32.448 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:32.449 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:34.583 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:34.587 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:34.590 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:34.591 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:47.588 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:47.593 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:47.599 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:47.600 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:53.130 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:53.136 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:53.139 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:53.140 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:10:55.877 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:10:55.882 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:10:55.886 [info] index finished after resolve  [object Object] 
2025-07-31 17:10:55.886 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:12.146 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:12.152 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:12.155 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:12.156 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:15.156 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:15.161 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:15.167 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:15.168 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:20.144 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:20.149 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:20.152 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:20.153 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:22.240 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:22.245 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:22.249 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:22.250 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:28.828 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:28.835 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:28.837 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:28.838 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:35.308 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:35.313 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:35.314 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:35.315 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:37.806 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:37.811 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:37.811 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:37.812 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:11:52.447 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:11:52.451 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:11:52.454 [info] index finished after resolve  [object Object] 
2025-07-31 17:11:52.455 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:12:42.026 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:12:42.030 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:12:42.035 [info] index finished after resolve  [object Object] 
2025-07-31 17:12:42.036 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:12:44.113 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:12:44.117 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:12:44.121 [info] index finished after resolve  [object Object] 
2025-07-31 17:12:44.122 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:12:50.497 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:12:50.502 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:12:50.505 [info] index finished after resolve  [object Object] 
2025-07-31 17:12:50.506 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:12:52.578 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:12:52.582 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:12:52.583 [info] index finished after resolve  [object Object] 
2025-07-31 17:12:52.584 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:12:54.639 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:12:54.643 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:12:54.644 [info] index finished after resolve  [object Object] 
2025-07-31 17:12:54.644 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:01.025 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:01.030 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:01.030 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:01.031 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:03.066 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:03.071 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:03.072 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:03.072 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:05.302 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:05.306 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:05.310 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:05.311 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:07.327 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:07.331 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:07.334 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:07.335 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:18.805 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:18.811 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:18.812 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:18.813 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:24.995 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:25.003 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:25.004 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:25.004 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:13:42.081 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:13:42.088 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:13:42.094 [info] index finished after resolve  [object Object] 
2025-07-31 17:13:42.095 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:14:14.490 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:14:14.495 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:14:14.499 [info] index finished after resolve  [object Object] 
2025-07-31 17:14:14.500 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:14:16.732 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:14:16.967 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:14:16.968 [info] index finished after resolve  [object Object] 
2025-07-31 17:14:16.969 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:14:48.655 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:14:48.663 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:14:48.665 [info] index finished after resolve  [object Object] 
2025-07-31 17:14:48.666 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 17:14:51.684 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 17:14:51.690 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 17:14:51.691 [info] index finished after resolve  [object Object] 
2025-07-31 17:14:51.691 [info] refresh page data from resolve listeners 0 1017   
