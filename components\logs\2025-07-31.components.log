2025-07-31 09:32:53.166 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:32:53.175 [info] indexing created file components/logs/2025-07-31.components.log  [object Object] 
2025-07-31 09:32:53.177 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:32:53.179 [info] refresh page data from created listeners 0 1017   
2025-07-31 09:32:53.180 [info] index finished after resolve  [object Object] 
2025-07-31 09:32:53.181 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:32:56.306 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:32:56.338 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:32:56.340 [info] index finished after resolve  [object Object] 
2025-07-31 09:32:56.341 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:32:59.015 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:32:59.031 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:32:59.033 [info] index finished after resolve  [object Object] 
2025-07-31 09:32:59.034 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:33:01.509 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:33:01.526 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:33:01.527 [info] index finished after resolve  [object Object] 
2025-07-31 09:33:01.528 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:16.641 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:16.658 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:16.659 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:16.660 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:18.792 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:18.820 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:18.821 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:18.821 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:20.826 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:20.846 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:20.850 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:20.850 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:23.282 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:23.289 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:23.289 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:23.290 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:34.288 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:34.292 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:34.293 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:34.293 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:37.214 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:37.218 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:37.219 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:37.220 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:39.211 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:39.214 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:39.215 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:39.216 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:42.634 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:42.638 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:42.638 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:42.639 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:46.830 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:46.835 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:46.837 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:46.837 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:51.990 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:51.995 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:51.996 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:51.997 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:55.143 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:55.147 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:55.148 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:55.148 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:57.228 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:57.235 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:57.235 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:57.236 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:38:59.291 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:38:59.295 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:38:59.295 [info] index finished after resolve  [object Object] 
2025-07-31 09:38:59.296 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:01.391 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:01.395 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:01.396 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:01.396 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:06.868 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:06.872 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:06.872 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:06.873 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:08.904 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:08.908 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:08.910 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:08.910 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:11.584 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:11.588 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:11.589 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:11.589 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:13.851 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:13.856 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:13.857 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:13.857 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:16.283 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:16.286 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:16.286 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:16.287 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:19.562 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:19.568 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:19.569 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:19.569 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:23.357 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:23.375 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:23.375 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:23.376 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:26.174 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:26.179 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:26.180 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:26.181 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:31.725 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:31.729 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:31.730 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:31.731 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:33.787 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:33.790 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:33.791 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:33.792 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:36.165 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:36.169 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:36.170 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:36.170 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:38.416 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:38.419 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:38.421 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:38.421 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:41.361 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:41.365 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:41.366 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:41.367 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:43.536 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:43.540 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:43.540 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:43.541 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:39:59.291 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:39:59.309 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:39:59.312 [info] index finished after resolve  [object Object] 
2025-07-31 09:39:59.312 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:02.703 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:02.708 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:02.709 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:02.709 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:05.356 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:05.360 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:05.361 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:05.362 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:07.509 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:07.513 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:07.514 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:07.514 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:09.583 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:09.587 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:09.588 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:09.588 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:11.661 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:11.665 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:11.666 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:11.666 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:13.799 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:13.803 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:13.805 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:13.806 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:17.667 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:17.670 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:17.671 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:17.672 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:30.174 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:30.178 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:30.179 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:30.180 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:33.124 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:33.128 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:33.129 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:33.130 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:35.345 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:35.349 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:35.350 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:35.350 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:37.486 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:37.490 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:37.491 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:37.492 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:40:59.627 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:40:59.631 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:40:59.632 [info] index finished after resolve  [object Object] 
2025-07-31 09:40:59.633 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:01.921 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:01.925 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:01.926 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:01.927 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:04.543 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:04.549 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:04.550 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:04.550 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:06.640 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:06.644 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:06.645 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:06.645 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:09.067 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:09.070 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:09.071 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:09.071 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:11.635 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:11.639 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:11.640 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:11.640 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:13.925 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:13.929 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:13.930 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:13.931 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:16.350 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:16.353 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:16.354 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:16.354 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:20.482 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:20.486 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:20.486 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:20.487 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:26.938 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:26.941 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:26.943 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:26.944 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:30.497 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:30.501 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:30.502 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:30.502 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:32.917 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:32.921 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:32.922 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:32.922 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:35.587 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:35.591 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:35.592 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:35.592 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:38.031 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:38.034 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:38.035 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:38.035 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:42.667 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:42.672 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:42.673 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:42.673 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:44.768 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:44.773 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:44.773 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:44.774 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:46.826 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:46.830 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:46.831 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:46.831 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:49.009 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:49.013 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:49.014 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:49.015 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:51.096 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:51.100 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:51.101 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:51.101 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:41:53.965 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:41:53.969 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:41:53.970 [info] index finished after resolve  [object Object] 
2025-07-31 09:41:53.970 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:52.586 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:52.590 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:52.591 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:52.591 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:54.909 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:54.913 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:54.916 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:54.921 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:57.086 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:57.090 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:57.091 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:57.091 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:45:59.577 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:45:59.581 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:45:59.583 [info] index finished after resolve  [object Object] 
2025-07-31 09:45:59.583 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:01.786 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:01.790 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:01.791 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:01.792 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:08.315 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:08.319 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:08.319 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:08.320 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:10.472 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:10.476 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:10.477 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:10.478 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:12.608 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:12.612 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:12.613 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:12.613 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:14.681 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:14.685 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:14.686 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:14.687 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:16.726 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:16.729 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:16.730 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:16.730 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:18.789 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:18.793 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:18.793 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:18.794 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:20.862 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:20.866 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:20.867 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:20.867 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:46:22.885 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:46:22.889 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:46:22.890 [info] index finished after resolve  [object Object] 
2025-07-31 09:46:22.890 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:09.279 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:09.283 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:09.284 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:09.284 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:31.061 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:31.065 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:31.066 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:31.067 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:34.512 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:34.516 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:34.517 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:34.517 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:37.499 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:37.501 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:37.502 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:37.502 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:40.543 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:40.547 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:40.548 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:40.548 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:43.286 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:43.290 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:43.291 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:43.291 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:47.100 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:47.104 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:47.105 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:47.105 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:49.297 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:49.301 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:49.302 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:49.302 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:51.682 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:51.686 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:51.686 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:51.687 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:53.972 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:53.976 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:53.976 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:53.977 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:48:56.057 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:48:56.061 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:48:56.062 [info] index finished after resolve  [object Object] 
2025-07-31 09:48:56.062 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:00.188 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:00.192 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:00.193 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:00.194 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:08.125 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:08.128 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:08.129 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:08.129 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:10.528 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:10.530 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:10.531 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:10.531 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:12.836 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:12.841 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:12.842 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:12.842 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:17.276 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:17.279 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:17.280 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:17.281 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:20.833 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:20.837 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:20.838 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:20.839 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:22.875 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:22.880 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:22.881 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:22.881 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:27.754 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:27.757 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:27.758 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:27.759 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:29.956 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:29.961 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:29.961 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:29.962 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:32.002 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:32.006 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:32.008 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:32.009 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:35.046 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:35.049 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:35.050 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:35.051 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:37.323 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:37.328 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:37.330 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:37.330 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:39.599 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:39.602 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:39.603 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:39.603 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:43.541 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:43.545 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:43.546 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:43.546 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:45.697 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:45.700 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:45.702 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:45.702 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:50.209 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:50.213 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:50.214 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:50.214 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:52.321 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:52.325 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:52.325 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:52.326 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:49:54.353 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:49:54.357 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:49:54.358 [info] index finished after resolve  [object Object] 
2025-07-31 09:49:54.358 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:50:15.744 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:50:15.748 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:50:15.749 [info] index finished after resolve  [object Object] 
2025-07-31 09:50:15.749 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 09:50:17.861 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 09:50:17.866 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 09:50:17.867 [info] index finished after resolve  [object Object] 
2025-07-31 09:50:17.867 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:24.723 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:24.751 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:24.752 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:24.753 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:27.326 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:27.329 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:27.330 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:27.331 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:29.831 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:29.835 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:29.835 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:29.836 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:31.899 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:31.905 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:31.906 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:31.907 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:33.960 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:33.965 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:33.965 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:33.966 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:35.992 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:35.996 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:35.997 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:35.998 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:38.365 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:38.369 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:38.370 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:38.370 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:41.571 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:41.577 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:41.578 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:41.578 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:43.615 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:43.619 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:43.620 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:43.621 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:46.179 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:46.184 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:46.184 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:46.185 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:48.654 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:48.657 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:48.658 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:48.658 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:50.813 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:50.819 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:50.820 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:50.820 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:52.882 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:52.887 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:52.888 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:52.888 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:22:54.979 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:22:54.982 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:22:54.982 [info] index finished after resolve  [object Object] 
2025-07-31 10:22:54.983 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:03.062 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:03.066 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:03.067 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:03.067 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:09.556 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:09.560 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:09.561 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:09.561 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:11.919 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:11.924 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:11.926 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:11.926 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:13.920 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:13.924 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:13.925 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:13.925 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:15.994 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:15.998 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:15.999 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:15.999 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:18.401 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:18.407 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:18.408 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:18.408 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:20.514 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:20.519 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:20.519 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:20.520 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:22.547 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:22.551 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:22.553 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:22.553 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:24.615 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:24.620 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:24.621 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:24.622 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:26.639 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:26.643 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:26.644 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:26.645 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:32.456 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:32.459 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:32.460 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:32.460 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:34.539 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:34.542 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:34.543 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:34.544 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:36.644 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:36.648 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:36.649 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:36.650 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:41.631 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:41.638 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:41.639 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:41.639 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:46.760 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:46.765 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:46.765 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:46.766 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:54.165 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:54.169 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:54.170 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:54.170 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:23:58.150 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:23:58.154 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:23:58.155 [info] index finished after resolve  [object Object] 
2025-07-31 10:23:58.155 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:05.321 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:05.348 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:05.349 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:05.350 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:09.369 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:09.375 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:09.376 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:09.377 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:14.197 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:14.201 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:14.202 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:14.202 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 10:24:16.231 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-07-31 10:24:16.234 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-31 10:24:16.236 [info] index finished after resolve  [object Object] 
2025-07-31 10:24:16.237 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:17:54.506 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:17:54.624 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:17:54.629 [info] index finished after resolve  [object Object] 
2025-07-31 15:17:54.630 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:18:02.778 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-31 15:18:03.027 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-31 15:18:03.030 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-31 15:18:03.033 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-31 15:18:03.038 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-31 15:19:08.447 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:19:08.463 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:19:08.470 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:08.471 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:10.597 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:19:10.608 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:19:10.610 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:10.610 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:15.278 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:19:15.290 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:19:15.291 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:15.291 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:53.241 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:19:53.246 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:19:53.247 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:53.247 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:19:55.362 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:19:55.366 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:19:55.367 [info] index finished after resolve  [object Object] 
2025-07-31 15:19:55.367 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:06.533 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:06.536 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:06.538 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:06.538 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:08.625 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:08.629 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:08.630 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:08.630 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:11.877 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:11.882 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:11.883 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:11.883 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:14.040 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:14.047 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:14.047 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:14.048 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:21.446 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:21.450 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:21.451 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:21.452 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:23.950 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:23.955 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:23.957 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:23.957 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:28.076 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:28.080 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:28.081 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:28.081 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:30.197 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:30.201 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:30.203 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:30.203 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:33.560 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:33.565 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:33.566 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:33.566 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:35.807 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:35.811 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:35.812 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:35.812 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:37.991 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:37.994 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:37.995 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:37.995 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:56.837 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:56.842 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:56.843 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:56.844 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:20:59.690 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:20:59.694 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:20:59.695 [info] index finished after resolve  [object Object] 
2025-07-31 15:20:59.695 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:03.414 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:03.469 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:03.470 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:03.471 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:07.526 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:07.582 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:07.584 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:07.585 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:20.037 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:20.041 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:20.043 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:20.043 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:23.258 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:23.262 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:23.263 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:23.264 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:32.923 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:32.927 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:32.928 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:32.929 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:46.734 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:46.739 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:46.740 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:46.741 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:53.580 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:53.585 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:53.586 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:53.587 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:21:56.929 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:21:56.934 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:21:56.936 [info] index finished after resolve  [object Object] 
2025-07-31 15:21:56.936 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:00.600 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:00.605 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:00.607 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:00.607 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:03.863 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:03.867 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:03.868 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:03.869 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:05.955 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:05.960 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:05.960 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:05.961 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:08.021 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:08.025 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:08.026 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:08.027 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:22:20.910 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-31 15:22:21.088 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-31 15:22:21.089 [info] index finished after resolve  [object Object] 
2025-07-31 15:22:21.090 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:23.484 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:23.493 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:23.494 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:23.494 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:26.597 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:26.608 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:26.609 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:26.610 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:31.435 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:31.443 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:31.443 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:31.444 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:39.631 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:39.633 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:39.634 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:39.635 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:53.897 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:53.904 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:53.905 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:53.906 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:23:58.049 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:23:58.056 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:23:58.058 [info] index finished after resolve  [object Object] 
2025-07-31 15:23:58.059 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:24:00.197 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:24:00.204 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:24:00.205 [info] index finished after resolve  [object Object] 
2025-07-31 15:24:00.205 [info] refresh page data from resolve listeners 0 1017   
2025-07-31 15:24:08.775 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-31 15:24:08.778 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-31 15:24:08.779 [info] index finished after resolve  [object Object] 
2025-07-31 15:24:08.779 [info] refresh page data from resolve listeners 0 1017   
