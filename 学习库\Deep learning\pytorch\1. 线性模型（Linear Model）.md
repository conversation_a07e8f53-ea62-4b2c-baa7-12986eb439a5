---
tags:
  - 学习
  - deep_learning
  - pytorch
---

##  问题引入

- 场景假设：学生的期末考试分数(y)取决于它们在课程上花费的时间(x)
- 数据：
  - 学生的学习时间：`[1, 2, 3, 4]`
  - 学生的考试分数：`[2, 4, 6, y]`
- 问题：预测学生的考试分数，花费的时间为4小时会得到多少分数
- 目标：使用已知数据（训练集，TrainingSet）来预测未知数据（测试集，TestSet）下的结果
- 方法：这属于监督学习（Supervised Learning），我们可以使用线性模型来进行预测

## 模型设计

选择线性模型(Linear Model)来进行预测是一个可能的选择，线性模型的公式为：

- 通用形式： $\hat{y}  = \omega \times x + b$
  - $\hat{y}$：预测值
  - $\omega$：权重
  - $b$：偏置
  - $x$：输入值
- 简化模型（本例）： $\hat{y}  = \omega \times x$


## 线性回归与初始猜想

- 可视化：将训练数据(1,2), (2,4), (3,6)绘制在图上，它们会落在一条直线上(y=2x)，将该直线称之为`True Line`，它是我们要学习的目标
- 机器学习的起点：模型刚开始并不知道$\omega$的确切值，它会从一个随机猜想的值开始，比如$\omega=0.5$，然后根据训练数据来调整$\omega$的值

## 损失函数(Loss Function)与误差函数(Cost Function)

![[1. 线性模型-2025-04-27-15-04-26.png|535x235]]

如图所示,误差(Error)是值 $\hat{y}$ 与真实值 $y$ 之间的差距,即 $\hat{y}-y$。而损失函数(Loss Function)是误差的平方即：
$$
loss = (\hat{y}-y)^2 = (\omega \times x - y)^2
$$
损失函数是一个非负数，越小越好。==我们希望通过调整模型参数 $\omega$ 来最小化损失函数。==
而这只是一个样本上的损失函数，通常我们会在整个训练集上计算平均损失函数，即**均方误差(Mean Squared Error, MSE)**，也就是 cost 函数：
$$
MSE = cost = \frac{1}{n} \sum_{i=1}^{n} (\hat{y}_i - y_i)^2
$$

````ad-example
title: 举例
collapse:close
假设有三个预测的线性模型，它们的权重分别为 $\omega_1 = 3$、$\omega_2 = 4$ 和 $\omega_3 = 0$，那么可以分别求出它们的预测值 $\hat{y_{1}}$ , $\hat{y_{2}}$ 和 $\hat{y_{3}}$ 在不同的 $x$ 值下的损失函数值。假设 $x = 1,2,3$，那么可以得到以下结果：

```ad-col2
color:#e84393
title: 权重$w$ =3
collapse: close
- $x=1$时，$y=2$，$\hat{y}=3$，$loss=(3-2)^2=1$
- $x=2$时，$y=4$，$\hat{y}=6$，$loss=(6-4)^2=4$
- $x=3$时，$y=6$，$\hat{y}=9$，$loss=(9-6)^2=9$

| $x$ | $y$ | $\hat{y}$ | $loss$ |
| --- | --- | --------- | ------ |
| 1   | 2   | 3         | 1      |
| 2   | 4   | 6         | 4      |
| 3   | 6   | 9         | 9      |

```

```ad-col2
title: 权重$w$ =4
color:#e84393
collapse: close
- $x=1$时，$y=2$，$\hat{y}=4$，$loss=(4-2)^2=4$
- $x=2$时，$y=4$，$\hat{y}=8$，$loss=(8-4)^2=16$
- $x=3$时，$y=6$，$\hat{y}=12$，$loss=(12-6)^2=36$

| $x$ | $y$ | $\hat{y}$ | $loss$ |
| --- | --- | --------- | ------ |
| 1   | 2   | 4         | 4      |
| 2   | 4   | 8         | 16     |
| 3   | 6   | 12        | 36     |

```

```ad-col2
title: 权重$w$为0
color:#e84393
collapse: close
- $x=1$时，$y=2$，$\hat{y}=0$，$loss=(0-2)^2=4$
- $x=2$时，$y=4$，$\hat{y}=0$，$loss=(0-4)^2=16$
- $x=3$时，$y=6$，$\hat{y}=0$，$loss=(0-6)^2=36$

| $x$ | $y$ | $\hat{y}$ | $loss$ |
| --- | --- | --------- | ------ |
| 1   | 2   | 0         | 4      |
| 2   | 4   | 0         | 16     |
| 3   | 6   | 0         | 36     |

```

```ad-col2
title: 分别求MSE（均方差误差）
color:#e84393
collapse: close
- $w$ =3, $MSE=\frac{1}{3}(1+4+9)=4.67$
- $w$ =4, $MSE=\frac{1}{3}(4+16+36)=18.67$
- $w$ =0, $MSE=\frac{1}{3}(4+16+36)=18.67$

| $w$ | $MSE$ |
| --- | ----- |
| 3   | 4.67  |
| 4   | 18.67 |
| 0   | 18.67 |
```
通过上面的计算可以看出，$w=3$ 时的平均损失函数值最小，因此我们可以认为 $w=3$ 是一个比较好的初始猜想。



````




